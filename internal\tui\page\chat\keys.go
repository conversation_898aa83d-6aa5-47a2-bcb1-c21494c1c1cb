package chat

import (
	"github.com/charmbracelet/bubbles/v2/key"
)

type KeyMap struct {
	NewSession    key.Binding
	AddAttachment key.Binding
	Cancel        key.Binding
	Tab           key.Binding
	Details       key.Binding
}

func DefaultKeyMap() KeyMap {
	return KeyMap{
		NewSession: key.NewBinding(
			key.<PERSON><PERSON><PERSON><PERSON>("ctrl+n"),
			key.WithHelp("ctrl+n", "new session"),
		),
		AddAttachment: key.NewBinding(
			key.WithKeys("ctrl+f"),
			key.WithHelp("ctrl+f", "add attachment"),
		),
		Cancel: key.NewBinding(
			key.<PERSON><PERSON><PERSON><PERSON>("esc"),
			key.WithHelp("esc", "cancel"),
		),
		Tab: key.NewBinding(
			key.With<PERSON>eys("tab"),
			key.WithHelp("tab", "change focus"),
		),
		Details: key.NewBinding(
			key.<PERSON><PERSON><PERSON><PERSON>("ctrl+d"),
			key.<PERSON><PERSON>el<PERSON>("ctrl+d", "toggle details"),
		),
	}
}
