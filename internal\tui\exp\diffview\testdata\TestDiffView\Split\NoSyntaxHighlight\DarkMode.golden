[48;2;73;73;255m [m[38;2;191;188;200;48;2;73;73;255m …[m[48;2;73;73;255m [m[38;2;191;188;200;48;2;51;49;178m  @@ -2,6 +2,7 @@ [m[48;2;51;49;178m                                [m[48;2;73;73;255m [m[38;2;191;188;200;48;2;73;73;255m …[m[48;2;73;73;255m [m[38;2;191;188;200;48;2;51;49;178m [m[48;2;51;49;178m                                                 [m
[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m 2[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  [m[48;2;32;31;38m                                                [m[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m 2[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  [m[48;2;32;31;38m                                                [m
[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m 3[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  import ([m[48;2;32;31;38m                                        [m[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m 3[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  import ([m[48;2;32;31;38m                                        [m
[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m 4[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m      "fmt"[m[48;2;32;31;38m                                       [m[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m 4[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m      "fmt"[m[48;2;32;31;38m                                       [m
[48;2;58;57;67m [m[48;2;58;57;67m  [m[48;2;58;57;67m [m[48;2;58;57;67m  [m[48;2;58;57;67m                                                [m[48;2;41;50;41m [m[38;2;10;220;217;48;2;41;50;41m 5[m[48;2;41;50;41m [m[38;2;10;220;217;48;2;48;58;48m+ [m[38;2;241;239;239;48;2;48;58;48m    "strings"[m[48;2;48;58;48m                                   [m
[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m 5[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  )[m[48;2;32;31;38m                                               [m[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m 6[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  )[m[48;2;32;31;38m                                               [m
[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m 6[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  [m[48;2;32;31;38m                                                [m[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m 7[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  [m[48;2;32;31;38m                                                [m
[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m 7[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  func main() {[m[48;2;32;31;38m                                   [m[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m 8[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  func main() {[m[48;2;32;31;38m                                   [m
[48;2;73;73;255m [m[38;2;191;188;200;48;2;73;73;255m …[m[48;2;73;73;255m [m[38;2;191;188;200;48;2;51;49;178m  @@ -9,5 +10,6 @@ [m[48;2;51;49;178m                               [m[48;2;73;73;255m [m[38;2;191;188;200;48;2;73;73;255m …[m[48;2;73;73;255m [m[38;2;191;188;200;48;2;51;49;178m [m[48;2;51;49;178m                                                 [m
[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m 9[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  }[m[48;2;32;31;38m                                               [m[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m10[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  }[m[48;2;32;31;38m                                               [m
[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m10[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  [m[48;2;32;31;38m                                                [m[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m11[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  [m[48;2;32;31;38m                                                [m
[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m11[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  func getContent() string {[m[48;2;32;31;38m                      [m[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m12[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  func getContent() string {[m[48;2;32;31;38m                      [m
[48;2;51;41;41m [m[38;2;255;56;139;48;2;51;41;41m12[m[48;2;51;41;41m [m[38;2;255;56;139;48;2;58;48;48m- [m[38;2;241;239;239;48;2;58;48;48m    return "Hello, world!"[m[48;2;58;48;48m                      [m[48;2;41;50;41m [m[38;2;10;220;217;48;2;41;50;41m13[m[48;2;41;50;41m [m[38;2;10;220;217;48;2;48;58;48m+ [m[38;2;241;239;239;48;2;48;58;48m    content := strings.ToUpper("Hello, World!")[m[48;2;48;58;48m [m
[48;2;58;57;67m [m[48;2;58;57;67m  [m[48;2;58;57;67m [m[48;2;58;57;67m  [m[48;2;58;57;67m                                                [m[48;2;41;50;41m [m[38;2;10;220;217;48;2;41;50;41m14[m[48;2;41;50;41m [m[38;2;10;220;217;48;2;48;58;48m+ [m[38;2;241;239;239;48;2;48;58;48m    return content[m[48;2;48;58;48m                              [m
[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m13[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  }[m[48;2;32;31;38m                                               [m[48;2;58;57;67m [m[38;2;223;219;221;48;2;58;57;67m15[m[48;2;58;57;67m [m[38;2;241;239;239;48;2;32;31;38m  }[m[48;2;32;31;38m                                               [m